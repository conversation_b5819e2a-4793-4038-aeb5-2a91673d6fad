import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { Subject, of, throwError } from 'rxjs';
import { DEVICE_ALREADY_CLIENT, DEVICE_ALREADY_DEMO, DEVICE_ALREADY_TEST, DEVICE_ALREADY_LOCKED, DEVICE_ALREADY_UNLOCKED, DEVICE_ALREADY_EDIT_ENABLE, DEVICE_ALREADY_EDIT_DISABLE, DEVICE_CONVERT_TO_CLIENT, DEVICE_CONVERT_TO_DEMO, DEVICE_CONVERT_TO_TEST, DeviceDetailResource, DeviceListResource, Device_Select_Message } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { AssignSelectedReleaseVersionRequest } from 'src/app/model/device/AssignSelectedReleaseVersionRequest.model';
import { DeviceFilterAction } from 'src/app/model/device/DeviceFilterAction.model';
import { DeviceSearchRequest } from 'src/app/model/device/deviceSearchRequest.model';
import { TransferProductDetails } from 'src/app/model/device/TransferProductDetails.model';
import { ReleaseVersionRequest } from 'src/app/model/release-version-request.model';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { DeviceOperationService } from './device-operation.service';
import { DeviceService } from 'src/app/shared/device.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { DeviceExportCSVSearchRequest } from 'src/app/model/device/DeviceExportCSVSearchRequest.model';
import { DeviceListOperations } from 'src/app/shared/enum/Operations/DeviceListOperations.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';

describe('DeviceOperationService', () => {
  let service: DeviceOperationService;
  let deviceService: jasmine.SpyObj<DeviceService>;
  let salesOrderApiCallService: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheService: jasmine.SpyObj<CountryCacheService>;
  let commonsService: jasmine.SpyObj<CommonsService>;
  let exceptionHandlingService: jasmine.SpyObj<ExceptionHandlingService>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let downloadService: jasmine.SpyObj<DownloadService>;
  let moduleValidationService: jasmine.SpyObj<ModuleValidationServiceService>;
  let permissionService: jasmine.SpyObj<PermissionService>;

  beforeEach(() => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    const deviceServiceSpy = jasmine.createSpyObj('DeviceService', [
      'getpackageVersion', 'getDeviceList', 'updateDeviceTypeToTest', 'updateDeviceTypeToClient',
      'updateDeviceTypeToDemo', 'updateDeviceState', 'editEnableDisableForDevice',
      'disableProductStatusForDevice', 'rmaProductStatusForDevice', 'associationDeviceWithSalesOrder',
      'generateCSVFileForDevice', 'downloadCSVFileForDevice', 'getDeviceDetail',
      'getReleaseVersionDetail', 'assignSelectedReleaseVersion'
    ]);
    const salesOrderApiCallServiceSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    const countryCacheServiceSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const commonsServiceSpy = jasmine.createSpyObj('CommonsService', [
      'checkForNull', 'checkValueIsNullOrEmpty', 'checkNullFieldValue', 'getIdsFromArray',
      'getSelectedValueFromEnum', 'getSelectedValueFromBooleanKeyValueMapping', 'getDeviceTypeStringToEnum'
    ]);
    const exceptionHandlingServiceSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const downloadServiceSpy = jasmine.createSpyObj('DownloadService', ['downloadExportCSV']);
    const moduleValidationServiceSpy = jasmine.createSpyObj('ModuleValidationServiceService', [
      'validateWithEditableWithMultipalRecoard', 'validateWithUserCountryForMultileRecord',
      'validateWithEditStateForSingleRecord', 'validateWithUserCountryForSingleRecord'
    ]);
    const permissionServiceSpy = jasmine.createSpyObj('PermissionService', ['getDevicePermission']);

    TestBed.configureTestingModule({
      providers: [
        { provide: DeviceService, useValue: deviceServiceSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderApiCallServiceSpy },
        { provide: CountryCacheService, useValue: countryCacheServiceSpy },
        { provide: CommonsService, useValue: commonsServiceSpy },
        { provide: ExceptionHandlingService, useValue: exceptionHandlingServiceSpy },
        { provide: DownloadService, useValue: downloadServiceSpy },
        { provide: ModuleValidationServiceService, useValue: moduleValidationServiceSpy },
        { provide: PermissionService, useValue: permissionServiceSpy },
        AuthJwtService,
        LocalStorageService,
        SessionStorageService,
        commonsProviders(toastrServiceMock)
      ]
    });

    service = TestBed.inject(DeviceOperationService);
    deviceService = TestBed.inject(DeviceService) as jasmine.SpyObj<DeviceService>;
    salesOrderApiCallService = TestBed.inject(SalesOrderApiCallService) as jasmine.SpyObj<SalesOrderApiCallService>;
    countryCacheService = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    commonsService = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    downloadService = TestBed.inject(DownloadService) as jasmine.SpyObj<DownloadService>;
    moduleValidationService = TestBed.inject(ModuleValidationServiceService) as jasmine.SpyObj<ModuleValidationServiceService>;
    permissionService = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  // ==================== BASIC FUNCTIONALITY TESTS ====================

  it('should get device list filter request parameter subject', () => {
    expect(service.getDeviceListFilterRequestParameterSubject()).toBeInstanceOf(Subject);
  });

  it('should get device list refresh subject', () => {
    expect(service.getDeviceListRefreshSubject()).toBeInstanceOf(Subject);
  });

  it('should set and get package version list', () => {
    const packageVersions = ['v1.0.0', 'v1.1.0', 'v2.0.0'];
    service.setPackageVersionList(packageVersions);
    expect(service.getPackageVersionList()).toEqual(packageVersions);
  });

  it('should set and get sales order number list', () => {
    const salesOrderNumbers = ['SO001', 'SO002', 'SO003'];
    service.setSalesOrderNumberList(salesOrderNumbers);
    expect(service.getSalesOrderNumberList()).toEqual(salesOrderNumbers);
  });

  it('should set and get country list', () => {
    const countries: CountryListResponse[] = [
      { id: 1, country: 'USA', languages: ['English'] },
      { id: 2, country: 'Canada', languages: ['English', 'French'] }
    ];
    service.setCountryList(countries);
    expect(service.getCountryList()).toEqual(countries);
  });

  it('should call device list filter request parameter subject', () => {
    const deviceSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceFilterAction = new DeviceFilterAction(listingPageReloadSubjectParameter, deviceSearchRequest);

    spyOn(service.getDeviceListFilterRequestParameterSubject(), 'next');

    service.callDeviceListFilterRequestParameterSubject(deviceFilterAction);

    expect(service.getDeviceListFilterRequestParameterSubject().next).toHaveBeenCalledWith(deviceFilterAction);
  });

  it('should call device list refresh subject', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service.getDeviceListRefreshSubject(), 'next');

    service.callDeviceListRefreshSubject(listingPageReloadSubjectParameter);

    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
  });

  // ==================== CACHE MANAGEMENT TESTS ====================

  it('should update cache in background with fresh API data', async () => {
    const apiPackageVersions = ['v3.0.0', 'v4.0.0'];
    const apiSalesOrders = ['SO004', 'SO005'];
    const apiCountries: CountryListResponse[] = [
      { id: 3, country: 'Germany', languages: ['German'] }
    ];

    deviceService.getpackageVersion.and.returnValue(of({ body: apiPackageVersions } as any));
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(apiSalesOrders));
    countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve(apiCountries));
    commonsService.checkForNull.and.returnValue(apiPackageVersions);

    await service.updateCacheInBackground();

    expect(service.getPackageVersionList()).toEqual(apiPackageVersions);
    expect(service.getSalesOrderNumberList()).toEqual(apiSalesOrders);
    expect(service.getCountryList()).toEqual(apiCountries);
    expect(deviceService.getpackageVersion).toHaveBeenCalled();
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(countryCacheService.getCountryListFromCache).toHaveBeenCalledWith(true);
  });

  it('should update only sales order cache when updateSalesOrderCacheOnly is called', async () => {
    service.setPackageVersionList(['v1.0.0']);
    service.setSalesOrderNumberList(['SO001']);
    service.setCountryList([{ id: 1, country: 'USA', languages: ['English'] }]);

    const newSalesOrders = ['SO001', 'SO002', 'SO003'];
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(newSalesOrders));

    await service.updateSalesOrderCacheOnly();

    expect(service.getSalesOrderNumberList()).toEqual(newSalesOrders);
    expect(service.getPackageVersionList()).toEqual(['v1.0.0']);
    expect(service.getCountryList()).toEqual([{ id: 1, country: 'USA', languages: ['English'] }]);
    expect(salesOrderApiCallService.getSalesOrderNumberList).toHaveBeenCalled();
    expect(deviceService.getpackageVersion).not.toHaveBeenCalled();
    expect(countryCacheService.getCountryListFromCache).not.toHaveBeenCalled();
  });

  it('should handle null response from package version API', async () => {
    const mockResponse = { body: null };
    deviceService.getpackageVersion.and.returnValue(of(mockResponse as any));
    salesOrderApiCallService.getSalesOrderNumberList.and.returnValue(Promise.resolve(['SO001']));
    countryCacheService.getCountryListFromCache.and.returnValue(Promise.resolve([{ id: 1, country: 'USA', languages: ['English'] }]));
    commonsService.checkForNull.and.returnValue(null);

    await service.updateCacheInBackground();

    expect(commonsService.checkForNull).toHaveBeenCalledWith(null);
    expect(service.getPackageVersionList()).toEqual([]);
  });

  // ==================== REFRESH PAGE SUBJECT TESTS ====================

  it('should call refresh page subject for DeviceListResource with filter hidden', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      deviceSearchRequest
    );

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
      jasmine.any(DeviceFilterAction)
    );
  });

  it('should call refresh page subject for DeviceListResource with filter not hidden', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service.getDeviceListRefreshSubject(), 'next');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      false,
      deviceSearchRequest
    );

    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
  });

  it('should call refresh page subject with clear filter', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      deviceSearchRequest
    );

    const expectedDeviceFilterAction = jasmine.objectContaining({
      deviceSearchRequest: jasmine.objectContaining({
        packageVersions: null
      })
    });

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(expectedDeviceFilterAction);
  });

  it('should call refresh page subject with null device search request', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      DeviceListResource,
      true,
      null
    );

    const expectedDeviceFilterAction = jasmine.objectContaining({
      deviceSearchRequest: jasmine.objectContaining({
        packageVersions: null
      })
    });

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(expectedDeviceFilterAction);
  });

  it('should not call any subject when resource is not DeviceListResource', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
    const deviceSearchRequest = new DeviceSearchRequest(['test'], null, null, null, null, null, null, null, null, null, null);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    service.callRefreshPageSubject(
      listingPageReloadSubjectParameter,
      'OTHER_RESOURCE',
      true,
      deviceSearchRequest
    );

    expect(service.callDeviceListFilterRequestParameterSubject).not.toHaveBeenCalled();
    expect(service.getDeviceListRefreshSubject().next).not.toHaveBeenCalled();
  });

  // ==================== DEVICE LIST OPERATIONS TESTS ====================

  it('should load device list successfully with permission', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = {
      status: 200,
      body: {
        content: [{ id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST_DEVICE', locked: false, productStatus: 'ENABLED' }],
        numberOfElements: 1,
        totalElements: 1
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(true);
    expect(result.devices).toEqual(mockResponse.body.content);
    expect(result.totalDeviceDisplay).toBe(1);
    expect(result.totalDevice).toBe(1);
    expect(result.localDeviceList).toEqual([{
      id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST_DEVICE', locked: false, productStatus: 'ENABLED'
    }]);
  });

  it('should return empty result when no permission', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };

    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe(0);
    expect(result.localDeviceList).toEqual([]);
  });

  it('should handle non-200 response status', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };
    const mockResponse = { status: 404, body: null };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(result.devices).toEqual([]);
    expect(result.totalDeviceDisplay).toBe(0);
    expect(result.totalDevice).toBe(0);
    expect(result.localDeviceList).toEqual([]);
  });

  it('should handle API error in loadDeviceList', async () => {
    const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
    const mockPageObj = { page: 0, size: 10 };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceList.and.returnValue(throwError('API Error'));

    const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

    expect(result.success).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== EXPORT CSV TESTS ====================

  it('should export device CSV successfully', async () => {
    const mockDeviceIds = [1, 2, 3];
    const mockGenerateResponse = { body: { fileName: 'devices.csv' } };
    const mockDownloadResponse = { body: 'csv,data' };

    deviceService.generateCSVFileForDevice.and.returnValue(of(mockGenerateResponse as any));
    deviceService.downloadCSVFileForDevice.and.returnValue(of(mockDownloadResponse as any));

    await service.exportDeviceCSV(mockDeviceIds);

    expect(deviceService.generateCSVFileForDevice).toHaveBeenCalledWith(jasmine.any(DeviceExportCSVSearchRequest));
    expect(deviceService.downloadCSVFileForDevice).toHaveBeenCalledWith('devices.csv');
    expect(downloadService.downloadExportCSV).toHaveBeenCalledWith("List_of_Device(s).xls", mockDownloadResponse);
  });

  it('should handle export error', async () => {
    const mockDeviceIds = [1, 2, 3];

    deviceService.generateCSVFileForDevice.and.returnValue(throwError('Export Error'));

    try {
      await service.exportDeviceCSV(mockDeviceIds);
      fail('Expected error to be thrown');
    } catch (error) {
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    }
  });

  // ==================== DEVICE TYPE CONVERSION TESTS ====================

  it('should convert devices to test type successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { deviceType: 'CLIENT_DEVICE', editable: true, country: 'USA' },
      { deviceType: 'DEMO_DEVICE', editable: true, country: 'USA' }
    ];
    const mockResponse = { status: 200, body: { message: 'Success' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToTest.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.updateDeviceTypeToTest).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_TEST);
    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
  });

  it('should show info message when devices are already test type', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'TEST_DEVICE', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith('This device is already Test device');
    expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
  });

  it('should return false when validation fails for convert to test', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT_DEVICE', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(false);

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
  });

  it('should return false when no permission for convert to test', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT_DEVICE', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceTypeToTest).not.toHaveBeenCalled();
  });

  it('should handle conversion error for convert to test', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT_DEVICE', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToTest.and.returnValue(throwError('Conversion Error'));

    try {
      await service.convertDevicesToTest(mockDeviceIds, mockSelectedDevices, DeviceListResource);
      fail('Expected error to be thrown');
    } catch (error) {
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    }
  });

  // ==================== CONVERT TO CLIENT TESTS ====================

  it('should convert devices to client type successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'TEST_DEVICE', editable: true, country: 'USA' }];
    const mockResponse = { status: 200, body: { message: 'Success' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToClient.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.convertDevicesToClient(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.updateDeviceTypeToClient).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_CLIENT);
  });

  it('should show info message when devices are already client type', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'CLIENT_DEVICE', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.convertDevicesToClient(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_CLIENT);
    expect(deviceService.updateDeviceTypeToClient).not.toHaveBeenCalled();
  });

  // ==================== CONVERT TO DEMO TESTS ====================

  it('should convert devices to demo type successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'TEST_DEVICE', editable: true, country: 'USA' }];
    const mockResponse = { status: 200, body: { message: 'Success' } };

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceTypeToDemo.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.convertDevicesToDemo(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDevicePermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
    expect(deviceService.updateDeviceTypeToDemo).toHaveBeenCalledWith(mockDeviceIds);
    expect(toastrServiceMock.success).toHaveBeenCalledWith(DEVICE_CONVERT_TO_DEMO);
  });

  it('should show info message when devices are already demo type', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ deviceType: 'DEMO_DEVICE', editable: true, country: 'USA' }];

    spyOn(service, 'validateDevicePermissions').and.returnValue(true);

    const result = await service.convertDevicesToDemo(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(DEVICE_ALREADY_DEMO);
    expect(deviceService.updateDeviceTypeToDemo).not.toHaveBeenCalled();
  });

  // ==================== LOCK/UNLOCK DEVICE TESTS ====================

  it('should lock/unlock devices successfully', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { locked: false, editable: true, country: 'USA' },
      { locked: false, editable: true, country: 'USA' }
    ];
    const mockResponse = { status: 200, body: { message: 'Devices locked successfully' } };

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceState.and.returnValue(of(mockResponse as any));
    spyOn(service.getDeviceListRefreshSubject(), 'next');

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceLockUnlockPermissions).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);
    expect(deviceService.updateDeviceState).toHaveBeenCalledWith(mockDeviceIds, true);
    expect(toastrServiceMock.success).toHaveBeenCalledWith('Devices locked successfully');
    expect(service.getDeviceListRefreshSubject().next).toHaveBeenCalled();
  });

  it('should return false when lock/unlock validation fails', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(false);

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceState).not.toHaveBeenCalled();
  });

  it('should return false when no lock permission', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
    expect(deviceService.updateDeviceState).not.toHaveBeenCalled();
  });

  it('should return false when response status is not 200', async () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];
    const mockResponse = { status: 400, body: { message: 'Error' } };

    spyOn(service, 'validateDeviceLockUnlockPermissions').and.returnValue(true);
    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.updateDeviceState.and.returnValue(of(mockResponse as any));

    const result = await service.lockUnlockDevices(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(false);
  });
  // ==================== DEVICE VALIDATION TESTS ====================

  it('should validate device selection with empty device IDs', () => {
    const result = service.validateDeviceSelection([], [], DeviceListResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Device_Select_Message);
  });

  it('should validate device selection successfully', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'USA' }
    ];

    spyOn(service, 'validateUserPermissionsAndCountry').and.returnValue(true);

    const result = service.validateDeviceSelection(mockDeviceIds, mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateUserPermissionsAndCountry).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
  });

  it('should validate user permissions and country', () => {
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'Canada' }
    ];

    moduleValidationService.validateWithEditableWithMultipalRecoard.and.returnValue(true);
    spyOn(service, 'validateUserCountryAccess').and.returnValue(true);

    const result = service.validateUserPermissionsAndCountry(mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithEditableWithMultipalRecoard).toHaveBeenCalled();
    expect(service.validateUserCountryAccess).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
  });

  it('should validate user country access', () => {
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'Canada' }
    ];

    moduleValidationService.validateWithUserCountryForMultileRecord.and.returnValue(true);

    const result = service.validateUserCountryAccess(mockSelectedDevices, DeviceListResource);

    expect(result).toBe(true);
    expect(moduleValidationService.validateWithUserCountryForMultileRecord).toHaveBeenCalledWith(['USA', 'Canada'], DeviceListResource, true);
  });

  it('should get device associated countries', () => {
    const mockSelectedDevices = [
      { editable: true, country: 'USA' },
      { editable: true, country: 'Canada' }
    ];

    const result = service.getDeviceAssociatedCountries(mockSelectedDevices);

    expect(result).toEqual(['USA', 'Canada']);
  });

  // ==================== DEVICE DETAIL OPERATIONS TESTS ====================

  it('should load device detail successfully', async () => {
    const mockDeviceId = 123;
    const mockResponse = {
      status: 200,
      body: {
        id: 123,
        deviceId: 'DEV001',
        salesOrderId: 456,
        deviceSerialNo: 'SN001',
        releaseId: 789
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(true);
    expect(result.deviceDetail).toEqual(mockResponse.body);
    expect(result.releaseVersionId).toBe(789);
    expect(result.transferProductDetails).toBeInstanceOf(TransferProductDetails);
  });

  it('should handle device detail with null releaseId', async () => {
    const mockDeviceId = 123;
    const mockResponse = {
      status: 200,
      body: {
        id: 123,
        deviceId: 'DEV001',
        salesOrderId: 456,
        deviceSerialNo: 'SN001',
        releaseId: null
      }
    };

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(true);
    expect(result.releaseVersionId).toBe(-1);
  });

  it('should return empty response when no permission for device detail', async () => {
    const mockDeviceId = 123;

    permissionService.getDevicePermission.and.returnValue(false);

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(false);
    expect(result.deviceDetail).toBe(null);
    expect(result.releaseVersionId).toBe(-1);
    expect(result.transferProductDetails).toBe(null);
  });

  it('should handle API error in device detail', async () => {
    const mockDeviceId = 123;

    permissionService.getDevicePermission.and.returnValue(true);
    deviceService.getDeviceDetail.and.returnValue(throwError('API Error'));

    const result = await service.loadDeviceDetail(mockDeviceId);

    expect(result.success).toBe(false);
    expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
  });

  // ==================== RELEASE VERSION TESTS ====================

  it('should get release versions for test device with permission', async () => {
    const mockRequest = new ReleaseVersionRequest(1, 'v1.0.0');
    const mockResponse = { body: [{ id: 1, version: 'v1.0.0' }] };

    deviceService.getReleaseVersionDetail.and.returnValue(of(mockResponse as any));

    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual(mockResponse.body);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should return empty result for non-test device', async () => {
    const result = await service.getReleaseVersions(deviceTypesEnum.CLIENT_DEVICE, 1, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should return empty result when no permission', async () => {
    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, 1, 'v1.0.0', false);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should return empty result when country ID is null', async () => {
    const result = await service.getReleaseVersions(deviceTypesEnum.TEST_DEVICE, null as any, 'v1.0.0', true);

    expect(result.success).toBe(true);
    expect(result.releaseVersions).toEqual([]);
    expect(result.selectedReleaseVersion).toBe(-1);
    expect(result.btnReleaseVersionDisable).toBe(true);
  });

  it('should assign release version successfully', async () => {
    const mockResponse = { status: 200 };

    deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

    await service.assignReleaseVersion(123, 456);

    expect(deviceService.assignSelectedReleaseVersion).toHaveBeenCalledWith(jasmine.any(AssignSelectedReleaseVersionRequest));
    expect(toastrServiceMock.success).toHaveBeenCalledWith("Release version assigned successfully");
  });

  it('should handle assign release version error', async () => {
    const mockResponse = { status: 400 };

    deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

    await service.assignReleaseVersion(123, 456);

    expect(toastrServiceMock.error).toHaveBeenCalledWith("Error in assigning Release version");
  });

  it('should disable assign button correctly', () => {
    expect(service.shouldDisableAssignButton(-1, 123)).toBe(true);
    expect(service.shouldDisableAssignButton(123, 123)).toBe(true);
    expect(service.shouldDisableAssignButton(456, 123)).toBe(false);
  });

  // ==================== FILTER INTEGRATION TESTS ====================

  it('should validate filter form with valid data', () => {
    const formValue = {
      deviceId: 'DEV001',
      deviceSerialNo: null,
      customerName: null,
      packageVersions: null,
      connectionState: null,
      deviceLockState: null,
      deviceEditState: null,
      countries: null,
      drpDeviceType: null,
      salesOrderNumber: null,
      productStatus: null
    };

    commonsService.checkValueIsNullOrEmpty.and.returnValue(false);

    const result = service.validateFilterForm(formValue);

    expect(result).toBe(true);
  });

  it('should validate filter form with all empty data', () => {
    const formValue = {
      deviceId: null,
      deviceSerialNo: null,
      customerName: null,
      packageVersions: null,
      connectionState: null,
      deviceLockState: null,
      deviceEditState: null,
      countries: null,
      drpDeviceType: null,
      salesOrderNumber: null,
      productStatus: null
    };

    commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

    const result = service.validateFilterForm(formValue);

    expect(result).toBe(false);
  });

  it('should build device search request from form data', () => {
    const formValue = {
      deviceId: 'DEV001',
      customerName: 'Test Customer',
      deviceSerialNo: 'SN001',
      countries: [{ id: 1, name: 'USA' }],
      productStatus: 'ENABLED',
      connectionState: 'ONLINE',
      deviceLockState: true,
      deviceEditState: false,
      packageVersions: ['v1.0.0'],
      drpDeviceType: 'TEST_DEVICE',
      salesOrderNumber: ['SO001']
    };

    commonsService.checkNullFieldValue.and.returnValues('DEV001', 'Test Customer', 'SN001', ['v1.0.0'], ['SO001']);
    commonsService.getIdsFromArray.and.returnValue([1]);
    commonsService.getSelectedValueFromEnum.and.returnValues(['ENABLED'], ['ONLINE']);
    commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValues(true, false);
    commonsService.getDeviceTypeStringToEnum.and.returnValue('TEST_DEVICE');

    const result = service.buildDeviceSearchRequest(formValue);

    expect(result).toBeInstanceOf(DeviceSearchRequest);
    expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('DEV001');
    expect(commonsService.getIdsFromArray).toHaveBeenCalledWith([{ id: 1, name: 'USA' }]);
  });

  it('should process filter search successfully', () => {
    const formValue = { deviceId: 'DEV001' };
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'validateFilterForm').and.returnValue(true);
    spyOn(service, 'buildDeviceSearchRequest').and.returnValue(new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null));
    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    const result = service.processFilterSearch(formValue, false, listingPageReloadSubjectParameter);

    expect(result).toBe(true);
    expect(service.validateFilterForm).toHaveBeenCalledWith(formValue);
    expect(service.buildDeviceSearchRequest).toHaveBeenCalledWith(formValue);
    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalled();
  });

  it('should return false when filter form is invalid', () => {
    const formValue = {};
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'validateFilterForm').and.returnValue(false);

    const result = service.processFilterSearch(formValue, true, listingPageReloadSubjectParameter);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
  });

  it('should clear all filters and refresh', () => {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

    spyOn(service, 'callDeviceListFilterRequestParameterSubject');

    service.clearAllFiltersAndRefresh(listingPageReloadSubjectParameter);

    expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
      jasmine.objectContaining({
        deviceSearchRequest: jasmine.objectContaining({
          packageVersions: null
        })
      })
    );
  });

  // ==================== DEVICE OPERATION HANDLER TESTS ====================

  it('should handle device operation for unlock devices', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['lockUnlock']);

    service.handleDeviceOperation(DeviceListOperations.UNLOCK_DEVICES, mockHandler);

    expect(mockHandler.lockUnlock).toHaveBeenCalledWith(false);
  });

  it('should handle device operation for lock devices', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['lockUnlock']);

    service.handleDeviceOperation(DeviceListOperations.LOCK_DEVICES, mockHandler);

    expect(mockHandler.lockUnlock).toHaveBeenCalledWith(true);
  });

  it('should handle device operation for convert to test', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['convertDataToTest']);

    service.handleDeviceOperation(DeviceListOperations.SET_DEVICE_TO_TEST, mockHandler);

    expect(mockHandler.convertDataToTest).toHaveBeenCalled();
  });

  it('should handle device operation for export CSV', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['exportCSV']);

    service.handleDeviceOperation(DeviceListOperations.Export_CSV, mockHandler);

    expect(mockHandler.exportCSV).toHaveBeenCalled();
  });

  it('should handle device operation for transfer device', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['transferDevice']);

    service.handleDeviceOperation(DeviceListOperations.TRANSFER_DEVICE, mockHandler);

    expect(mockHandler.transferDevice).toHaveBeenCalled();
  });

  it('should handle default case for device operation', () => {
    const mockHandler = jasmine.createSpyObj('DeviceOperationHandler', ['lockUnlock']);

    service.handleDeviceOperation('UNKNOWN_OPERATION', mockHandler);

    expect(mockHandler.lockUnlock).not.toHaveBeenCalled();
  });
});
































spyOn(service, 'validateDeviceSelection').and.returnValue(true);

const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, DeviceListResource);

expect(result).toBe(true);
expect(service.validateDeviceSelection).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
  });

it('should use validateSingleDevicePermissions for DeviceDetailResource', () => {
  const mockDeviceIds = [1];
  const mockSelectedDevices = [{ editable: true, country: 'USA' }];

  spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

  const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, DeviceDetailResource);

  expect(result).toBe(true);
  expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
});

it('should return false for unknown resource', () => {
  const mockDeviceIds = [1];
  const mockSelectedDevices = [{ editable: true, country: 'USA' }];

  const result = service.validateDevicePermissions(mockDeviceIds, mockSelectedDevices, 'UnknownResource');

  expect(result).toBe(false);
});
});

describe('validateDeviceLockUnlockPermissions', () => {
  it('should use validateDeviceSelection for DeviceListResource', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { locked: false, editable: true, country: 'USA' },
      { locked: false, editable: true, country: 'Canada' }
    ];

    spyOn(service, 'validateDeviceSelection').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateDeviceSelection).toHaveBeenCalledWith(mockDeviceIds, mockSelectedDevices, DeviceListResource);
  });

  it('should validate single device permissions for DeviceDetailResource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(true);
    expect(service.validateSingleDevicePermissions).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should return false when device is already locked and trying to lock', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ locked: true, editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith('Device already locked');
  });

  it('should return false when device is already unlocked and trying to unlock', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDevicePermissions').and.returnValue(true);

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, false, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith('Device already unlocked');
  });

  it('should return false for unknown resource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ locked: false, editable: true, country: 'USA' }];

    const result = service.validateDeviceLockUnlockPermissions(mockDeviceIds, mockSelectedDevices, true, 'UnknownResource');

    expect(result).toBe(false);
  });
});

describe('validateDeviceEnableDisablePermissions', () => {
  it('should use validateUserCountryAccess for DeviceListResource', () => {
    const mockDeviceIds = [1, 2];
    const mockSelectedDevices = [
      { editable: false, country: 'USA' },
      { editable: false, country: 'Canada' }
    ];

    spyOn(service, 'validateUserCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceListResource);

    expect(result).toBe(true);
    expect(service.validateUserCountryAccess).toHaveBeenCalledWith(mockSelectedDevices, DeviceListResource);
  });

  it('should validate single device country access for DeviceDetailResource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(true);
    expect(service.validateSingleDeviceCountryAccess).toHaveBeenCalledWith(mockSelectedDevices[0], DeviceDetailResource);
  });

  it('should return false when device is already enabled and trying to enable', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: true, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith('Device is already marked as editable.');
  });

  it('should return false when device is already disabled and trying to disable', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    spyOn(service, 'validateSingleDeviceCountryAccess').and.returnValue(true);

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, false, DeviceDetailResource);

    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith('Device is already marked as read-only.');
  });

  it('should return false for unknown resource', () => {
    const mockDeviceIds = [1];
    const mockSelectedDevices = [{ editable: false, country: 'USA' }];

    const result = service.validateDeviceEnableDisablePermissions(mockDeviceIds, mockSelectedDevices, true, 'UnknownResource');

    expect(result).toBe(false);
  });
});

describe('getDeviceAssociatedCountries', () => {
  it('should extract countries from selected devices', () => {
    const mockSelectedDevices = [
      { country: 'USA' },
      { country: 'Canada' },
      { country: 'USA' }
    ];

    const result = service.getDeviceAssociatedCountries(mockSelectedDevices);

    expect(result).toEqual(['USA', 'Canada', 'USA']);
  });
});
});

describe('Device Detail Operations', () => {
  describe('loadDeviceDetail', () => {
    it('should load device detail successfully', async () => {
      const mockDeviceId = 123;
      const mockDeviceDetail = {
        id: 123,
        deviceId: 'DEV001',
        salesOrderId: 456,
        deviceSerialNo: 'SN001',
        releaseId: 789
      };
      const mockResponse = { status: 200, body: mockDeviceDetail };

      deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

      const result = await service.loadDeviceDetail(mockDeviceId);

      expect(result.success).toBe(true);
      expect(result.deviceDetail).toEqual(jasmine.objectContaining({
        id: 123,
        deviceId: 'DEV001',
        salesOrderId: 456,
        deviceSerialNo: 'SN001',
        releaseId: 789
      }));
      expect(result.releaseVersionId).toBe(789);
      expect(result.transferProductDetails).toBeInstanceOf(TransferProductDetails);
    });

    it('should handle null release ID', async () => {
      const mockDeviceId = 123;
      const mockDeviceDetail = {
        id: 123,
        deviceId: 'DEV001',
        salesOrderId: 456,
        deviceSerialNo: 'SN001',
        releaseId: null
      };
      const mockResponse = { status: 200, body: mockDeviceDetail };

      deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

      const result = await service.loadDeviceDetail(mockDeviceId);

      expect(result.success).toBe(true);
      expect(result.releaseVersionId).toBe(-1);
    });

    it('should handle non-200 response', async () => {
      const mockDeviceId = 123;
      const mockResponse = { status: 404, body: null };

      deviceService.getDeviceDetail.and.returnValue(of(mockResponse as any));

      const result = await service.loadDeviceDetail(mockDeviceId);

      expect(result.success).toBe(false);
      expect(result.deviceDetail).toBe(null);
      expect(result.releaseVersionId).toBe(-1);
      expect(result.transferProductDetails).toBe(null);
    });

    it('should handle API error', async () => {
      const mockDeviceId = 123;

      deviceService.getDeviceDetail.and.returnValue(throwError(() => new Error('Detail Error')));

      await expectAsync(service.loadDeviceDetail(mockDeviceId)).toBeRejected();
      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalled();
    });
  });

  describe('assignReleaseVersion', () => {
    it('should assign release version successfully', async () => {
      const mockDeviceId = 123;
      const mockReleaseVersionId = 456;
      const mockResponse = { status: 200 };

      deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

      await service.assignReleaseVersion(mockDeviceId, mockReleaseVersionId);

      expect(deviceService.assignSelectedReleaseVersion).toHaveBeenCalledWith(
        new AssignSelectedReleaseVersionRequest(mockDeviceId, mockReleaseVersionId));
      expect(toastrServiceMock.success).toHaveBeenCalledWith('Release version assigned successfully');
    });

    it('should handle non-200 response', async () => {
      const mockDeviceId = 123;
      const mockReleaseVersionId = 456;
      const mockResponse = { status: 400 };

      deviceService.assignSelectedReleaseVersion.and.returnValue(of(mockResponse as any));

      await service.assignReleaseVersion(mockDeviceId, mockReleaseVersionId);

      expect(toastrServiceMock.error).toHaveBeenCalledWith('Error in assigning Release version');
    });
  });

  describe('shouldDisableAssignButton', () => {
    it('should return true when no release version selected', () => {
      const result = service.shouldDisableAssignButton(-1, 123);
      expect(result).toBe(true);
    });

    it('should return true when selected version equals current version', () => {
      const result = service.shouldDisableAssignButton(123, 123);
      expect(result).toBe(true);
    });

    it('should return false when different version selected', () => {
      const result = service.shouldDisableAssignButton(456, 123);
      expect(result).toBe(false);
    });
  });
});

describe('Filter Integration Logic', () => {
  describe('validateFilterForm', () => {
    it('should return true when at least one filter field has value', () => {
      const mockFormValue = {
        deviceId: 'DEV001',
        deviceSerialNo: '',
        customerName: '',
        packageVersions: null,
        connectionState: null,
        deviceLockState: null,
        deviceEditState: null,
        countries: null,
        drpDeviceType: null,
        salesOrderNumber: null,
        productStatus: null
      };

      commonsService.checkValueIsNullOrEmpty.and.callFake((value) => !value);

      const result = service.validateFilterForm(mockFormValue);

      expect(result).toBe(true);
    });

    it('should return false when all filter fields are empty', () => {
      const mockFormValue = {
        deviceId: '',
        deviceSerialNo: '',
        customerName: '',
        packageVersions: null,
        connectionState: null,
        deviceLockState: null,
        deviceEditState: null,
        countries: null,
        drpDeviceType: null,
        salesOrderNumber: null,
        productStatus: null
      };

      commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

      const result = service.validateFilterForm(mockFormValue);

      expect(result).toBe(false);
    });
  });

  describe('buildDeviceSearchRequest', () => {
    it('should build device search request from form values', () => {
      const mockFormValue = {
        deviceId: 'DEV001',
        deviceSerialNo: 'SN001',
        customerName: 'Customer1',
        packageVersions: ['v1.0.0'],
        connectionState: [{ value: 'ONLINE' }],
        deviceLockState: [{ value: true }],
        deviceEditState: [{ value: false }],
        countries: [{ id: 1, country: 'USA' }],
        drpDeviceType: 'TEST_DEVICE',
        salesOrderNumber: ['SO001'],
        productStatus: [{ value: 'ENABLED' }]
      };

      commonsService.checkNullFieldValue.and.callFake((value) => value || null);
      commonsService.getIdsFromArray.and.returnValue([1]);
      commonsService.getSelectedValueFromEnum.and.returnValue(['ENABLED']);
      commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(true);
      commonsService.getDeviceTypeStringToEnum.and.returnValue('TEST_DEVICE');

      const result = service.buildDeviceSearchRequest(mockFormValue);

      expect(result).toBeInstanceOf(DeviceSearchRequest);
      expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('DEV001');
      expect(commonsService.getIdsFromArray).toHaveBeenCalled();
      expect(commonsService.getSelectedValueFromEnum).toHaveBeenCalled();
    });
  });

  describe('processFilterSearch', () => {
    it('should process filter search successfully', () => {
      const mockFormValue = { deviceId: 'DEV001' };
      const mockListingParam = new ListingPageReloadSubjectParameter(true, true, false, false);

      spyOn(service, 'validateFilterForm').and.returnValue(true);
      spyOn(service, 'buildDeviceSearchRequest').and.returnValue(new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null));
      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      const result = service.processFilterSearch(mockFormValue, false, mockListingParam);

      expect(result).toBe(true);
      expect(service.validateFilterForm).toHaveBeenCalledWith(mockFormValue);
      expect(service.buildDeviceSearchRequest).toHaveBeenCalledWith(mockFormValue);
      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalled();
    });

    it('should return false when form is invalid', () => {
      const mockFormValue = {};
      const mockListingParam = new ListingPageReloadSubjectParameter(true, true, false, false);

      const result = service.processFilterSearch(mockFormValue, true, mockListingParam);

      expect(result).toBe(false);
      expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
    });

    it('should return false when validation fails', () => {
      const mockFormValue = {};
      const mockListingParam = new ListingPageReloadSubjectParameter(true, true, false, false);

      spyOn(service, 'validateFilterForm').and.returnValue(false);

      const result = service.processFilterSearch(mockFormValue, false, mockListingParam);

      expect(result).toBe(false);
      expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
    });
  });

  describe('clearAllFiltersAndRefresh', () => {
    it('should clear filters and refresh device list', () => {
      const mockListingParam = new ListingPageReloadSubjectParameter(true, true, false, false);

      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.clearAllFiltersAndRefresh(mockListingParam);

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.objectContaining({
          deviceSearchRequest: jasmine.objectContaining({
            packageVersions: null
          })
        })
      );
    });
  });
});

describe('Edge Cases and Error Handling', () => {
  it('should handle empty device arrays in validation', () => {
    const result = service.validateDeviceSelection([], [], DeviceListResource);
    expect(result).toBe(false);
    expect(toastrServiceMock.info).toHaveBeenCalledWith(Device_Select_Message);
  });

  it('should handle null device objects in validation', () => {
    moduleValidationService.validateWithEditStateForSingleRecord.and.returnValue(false);

    const result = service.validateSingleDevicePermissions(null, DeviceDetailResource);
    expect(result).toBe(false);
  });

  it('should handle undefined form values in filter validation', () => {
    commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

    const result = service.validateFilterForm(undefined);
    expect(result).toBe(false);
  });

  describe('buildDeviceSearchRequest', () => {
    it('should build device search request with all form values', () => {
      const mockFormValue = {
        deviceId: 'DEV001',
        customerName: 'Test Customer',
        deviceSerialNo: 'SN123',
        countries: [{ id: 1, country: 'USA' }],
        productStatus: [{ value: 'ENABLED' }],
        connectionState: [{ value: 'CONNECTED' }],
        deviceLockState: [{ key: true, value: 'Locked' }],
        deviceEditState: [{ key: false, value: 'Read Only' }],
        packageVersions: ['v1.0.0'],
        drpDeviceType: 'TEST_DEVICE',
        salesOrderNumber: ['SO001']
      };

      commonsService.checkNullFieldValue.and.callFake((value) => value || null);
      commonsService.getIdsFromArray.and.returnValue([1]);
      commonsService.getSelectedValueFromEnum.and.returnValue(['ENABLED']);
      commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(true);
      commonsService.getDeviceTypeStringToEnum.and.returnValue('TEST_DEVICE');

      const result = service.buildDeviceSearchRequest(mockFormValue);

      expect(result).toBeInstanceOf(DeviceSearchRequest);
      expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('DEV001');
      expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('Test Customer');
      expect(commonsService.checkNullFieldValue).toHaveBeenCalledWith('SN123');
      expect(commonsService.getIdsFromArray).toHaveBeenCalledWith([{ id: 1, country: 'USA' }]);
      expect(commonsService.getSelectedValueFromEnum).toHaveBeenCalledWith(jasmine.any(Array));
      expect(commonsService.getSelectedValueFromBooleanKeyValueMapping).toHaveBeenCalledWith(jasmine.any(Array));
      expect(commonsService.getDeviceTypeStringToEnum).toHaveBeenCalledWith('TEST_DEVICE');
    });

    it('should handle null and undefined form values', () => {
      const mockFormValue = {
        deviceId: null,
        customerName: undefined,
        deviceSerialNo: '',
        countries: null,
        productStatus: undefined,
        connectionState: [],
        deviceLockState: null,
        deviceEditState: undefined,
        packageVersions: null,
        drpDeviceType: null,
        salesOrderNumber: undefined
      };

      commonsService.checkNullFieldValue.and.returnValue(null);
      commonsService.getSelectedValueFromEnum.and.returnValue([]);
      commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
      commonsService.getDeviceTypeStringToEnum.and.returnValue(null);

      const result = service.buildDeviceSearchRequest(mockFormValue);

      expect(result).toBeInstanceOf(DeviceSearchRequest);
    });
  });

  describe('processFilterSearch', () => {
    it('should process filter search successfully', () => {
      const mockFormValue = {
        deviceId: 'DEV001',
        customerName: 'Test Customer'
      };
      const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      commonsService.checkValueIsNullOrEmpty.and.returnValue(false);
      commonsService.checkNullFieldValue.and.callFake((value) => value || null);
      commonsService.getSelectedValueFromEnum.and.returnValue([]);
      commonsService.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
      commonsService.getDeviceTypeStringToEnum.and.returnValue(null);
      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      const result = service.processFilterSearch(mockFormValue, false, mockListingPageReloadSubjectParameter);

      expect(result).toBe(true);
      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(jasmine.any(DeviceFilterAction));
    });

    it('should return false when form is invalid', () => {
      const mockFormValue = {};
      const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      const result = service.processFilterSearch(mockFormValue, true, mockListingPageReloadSubjectParameter);

      expect(result).toBe(false);
      expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
    });

    it('should return false when filter validation fails', () => {
      const mockFormValue = {};
      const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

      commonsService.checkValueIsNullOrEmpty.and.returnValue(true);

      const result = service.processFilterSearch(mockFormValue, false, mockListingPageReloadSubjectParameter);

      expect(result).toBe(false);
      expect(toastrServiceMock.info).toHaveBeenCalledWith('Please provide at least one filter criteria');
    });
  });

  describe('clearAllFiltersAndRefresh', () => {
    it('should clear all filters and refresh device list', () => {
      const mockListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
      spyOn(service, 'callDeviceListFilterRequestParameterSubject');

      service.clearAllFiltersAndRefresh(mockListingPageReloadSubjectParameter);

      expect(service.callDeviceListFilterRequestParameterSubject).toHaveBeenCalledWith(
        jasmine.objectContaining({
          deviceSearchRequest: jasmine.objectContaining({
            packageVersions: null,
            status: null,
            deviceType: null,
            deviceId: null,
            deviceSerialNo: null,
            customerName: null,
            countryIds: null,
            deviceLockStatus: null,
            isEditable: null,
            salesOrderNumbers: null,
            productStatus: null
          })
        })
      );
    });
  });

  describe('Private method coverage through processDeviceListResponse', () => {
    it('should process device list response correctly', async () => {
      const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      const mockPageObj = { page: 0, size: 10 };
      const mockResponse = {
        status: 200,
        body: {
          content: [
            { id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST_DEVICE', locked: false, productStatus: 'ENABLED' },
            { id: 2, deviceId: 'DEV002', editable: false, country: 'Canada', deviceType: 'CLIENT_DEVICE', locked: true, productStatus: 'DISABLED' }
          ],
          numberOfElements: 2,
          totalElements: 100
        }
      };

      deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

      const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

      expect(result.success).toBe(true);
      expect(result.devices).toEqual(mockResponse.body.content);
      expect(result.totalDeviceDisplay).toBe(2);
      expect(result.totalDevice).toBe(100);
      expect(result.localDeviceList).toEqual([
        { id: 1, deviceId: 'DEV001', editable: true, country: 'USA', deviceType: 'TEST_DEVICE', locked: false, productStatus: 'ENABLED' },
        { id: 2, deviceId: 'DEV002', editable: false, country: 'Canada', deviceType: 'CLIENT_DEVICE', locked: true, productStatus: 'DISABLED' }
      ]);
    });

    it('should handle empty device list response', async () => {
      const mockSearchRequest = new DeviceSearchRequest(null, null, null, null, null, null, null, null, null, null, null);
      const mockPageObj = { page: 0, size: 10 };
      const mockResponse = {
        status: 200,
        body: {
          content: [],
          numberOfElements: 0,
          totalElements: 0
        }
      };

      deviceService.getDeviceList.and.returnValue(of(mockResponse as any));

      const result = await service.loadDeviceList(mockSearchRequest, mockPageObj);

      expect(result.success).toBe(true);
      expect(result.devices).toEqual([]);
      expect(result.totalDeviceDisplay).toBe(0);
      expect(result.totalDevice).toBe(0);
      expect(result.localDeviceList).toEqual([]);
    });
  });
});
});
